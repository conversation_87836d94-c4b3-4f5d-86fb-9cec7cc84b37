# Payment Flow System

A comprehensive payment system for the Carwah dashboard that supports multiple payment methods including Hyperpay (Mada, Visa/Mastercard) and Tamara for agency users.

## Features

- **Multiple Payment Methods**: Supports Mada, Visa/Mastercard, and Tamara
- **Agency Support**: Designed specifically for agency users with proper validation
- **Extension & Installment Payments**: Handles rental extensions and installment payments
- **Real-time Status Tracking**: Polls payment status with automatic updates
- **Wallet Integration**: Supports partial wallet payments
- **Agency Deactivation Check**: Prevents payments when agency is deactivated
- **Responsive Design**: Works on desktop and mobile devices
- **Internationalization**: Supports Arabic and English

## Components

### PaymentFlow
Main component that orchestrates the entire payment process.

```jsx
<PaymentFlow
  isOpen={true}
  onClose={() => setModalOpen(false)}
  rentalId="123"
  extensionId="ext-456" // Optional for extensions
  isInstallment={false} // Optional for installments
  totalAmount={100}
  walletBalance={50}
  isAgencyDeactivated={false}
  onPaymentSuccess={(paymentMethod) => console.log('Success:', paymentMethod)}
  onPaymentError={(error) => console.log('Error:', error)}
/>
```

### PaymentMethodSelection
Component for selecting payment methods with wallet options.

### PaymentStatusModal
Displays payment status with appropriate messages and actions.

### TamaraWidget
Handles Tamara payment integration with widget loading.

### PaymentButton
Reusable button component for triggering payments.

```jsx
<PaymentButton
  rentalId="123"
  totalAmount={100}
  onPaymentSuccess={() => console.log('Payment successful')}
>
  Pay Now
</PaymentButton>
```

## Environment Variables

Required environment variables for the payment system:

```env
# Tamara Configuration
REACT_APP_TAMARA_PUBLIC_KEY=0aa13b77-e140-4e1c-9139-663443e4ecd1
REACT_APP_TAMARA_CDN=https://cdn-sandbox.tamara.co/widget-v2/tamara-widget.js

# Hyperpay Configuration
REACT_APP_HYPERPAY_URL=https://eu-test.oppwa.com/v1/paymentWidgets.js
REACT_APP_OPPWA_URL=https://eu-test.oppwa.com

# Base URL for payment result handling
REACT_APP_BASE_URL=https://your-domain.com

# Firebase Configuration for Remote Config (skip_integrity feature)
REACT_APP_FIREBASE_CONFIG='{"apiKey":"...","authDomain":"...","projectId":"...",...}'
```

## Payment Flow

1. **Method Selection**: User selects payment method (Mada, Credit Card, Tamara)
2. **Wallet Configuration**: Optional wallet amount selection
3. **Payment Processing**: 
   - Hyperpay: Loads payment widget
   - Tamara: Loads Tamara checkout widget
   - Wallet: Direct API call
4. **Status Monitoring**: Real-time polling of payment status
5. **Result Handling**: Success/failure callbacks with appropriate messaging

## GraphQL Mutations & Queries

### Regular Payments
- `getCheckoutId`: Get Hyperpay checkout ID
- `getPaymentStatus`: Check payment status
- `tamaraCreateCheckoutSession`: Create Tamara checkout
- `payByWallet`: Process wallet payment

### Extension Payments
- `extensionGetCheckoutId`: Get checkout ID for extensions
- `extensionGetPaymentStatus`: Check extension payment status
- `tamaraCreateExtensionCheckout`: Create Tamara checkout for extensions
- `extensionPayByWallet`: Process wallet payment for extensions

### Installment Payments
- `installmentGetCheckoutId`: Get checkout ID for installments
- `installmentGetPaymentStatus`: Check installment payment status
- `tamaraCreateInstallmentCheckout`: Create Tamara checkout for installments

## Agency Features

### Agency Deactivation Check
The system automatically checks if the current agency is deactivated and prevents payments:

```jsx
const { isAgencyDeactivated } = useAgencyStatus();
```

### Payment Notes
When an agency makes a payment, it's automatically noted in the booking details:
```
"(Through Agency [Agency-Name])"
```

## Firebase Remote Config Integration

### Skip Integrity Feature
The system integrates with Firebase Remote Config to support the `skip_integrity` feature, matching the web app behavior:

```jsx
const { remoteConfigValues } = useFirebase();
const { skip_integrity } = remoteConfigValues || {
  skip_integrity: { _value: "false" },
};
```

When `skip_integrity` is set to `"true"` in Firebase Remote Config:
- Payment widget scripts load without integrity attribute
- Bypasses SRI (Subresource Integrity) checks
- Useful for development or when CDN integrity hashes change

### Tamara Price Validation
The system validates Tamara payment eligibility based on Remote Config limits:

```jsx
const { isPriceEligible } = useTamaraEligibility(totalAmount);
```

**Remote Config Parameters:**
- `remove_tamara_limit`: Set to `"true"` to bypass all limits
- `tamara_min_limit_value`: Minimum amount for Tamara (e.g., `"100"`)
- `tamara_max_limit_value`: Maximum amount for Tamara (e.g., `"5000"`)

**Behavior:**
- Tamara option only appears when amount is within limits
- Shows informative message when not available
- Prevents Tamara payment attempts outside limits

### Firebase Setup
1. Configure Firebase project with Remote Config
2. Set `skip_integrity` parameter in Firebase Console
3. Add Firebase config to environment variables
4. The system automatically fetches and applies the setting

## Status Messages

The system uses the same status messages as the web app:

- **Success**: "Payment completed successfully"
- **Pending**: "Dear customer... Your payment process is currently pending by Hyperpay..."
- **Failed**: "Dear customer.. the payment process was unsuccessful, try to pay again"

## Testing

Run the test suite:

```bash
npm test src/components/PaymentFlow/__tests__/PaymentFlow.test.js
```

### Test Coverage
- Component rendering
- Payment method selection
- Agency deactivation handling
- Payment success/failure flows
- Extension and installment payments
- Cleanup and resource management

## Usage Examples

### Basic Rental Payment
```jsx
import PaymentFlow from 'components/PaymentFlow/PaymentFlow';

function BookingPage() {
  const [paymentOpen, setPaymentOpen] = useState(false);
  
  return (
    <div>
      <button onClick={() => setPaymentOpen(true)}>
        Pay Now
      </button>
      
      <PaymentFlow
        isOpen={paymentOpen}
        onClose={() => setPaymentOpen(false)}
        rentalId="123"
        totalAmount={500}
        onPaymentSuccess={() => {
          alert('Payment successful!');
          setPaymentOpen(false);
        }}
        onPaymentError={(error) => {
          alert(`Payment failed: ${error}`);
        }}
      />
    </div>
  );
}
```

### Extension Payment
```jsx
<PaymentFlow
  isOpen={true}
  onClose={() => setModalOpen(false)}
  rentalId="123"
  extensionId="ext-456"
  totalAmount={150}
  onPaymentSuccess={() => console.log('Extension paid')}
/>
```

### Installment Payment
```jsx
<PaymentFlow
  isOpen={true}
  onClose={() => setModalOpen(false)}
  rentalId="123"
  isInstallment={true}
  totalAmount={200}
  onPaymentSuccess={() => console.log('Installment paid')}
/>
```

## Security Considerations

1. **Environment Variables**: Never expose sensitive keys in client-side code
2. **Agency Validation**: Always verify agency status before processing payments
3. **Payment Verification**: Use server-side verification for payment status
4. **Error Handling**: Implement proper error boundaries and fallbacks
5. **Script Loading**: Secure script loading with integrity checks where possible

## Troubleshooting

### Common Issues

1. **Payment Widget Not Loading**
   - Check OPPWA_URL environment variable
   - Verify network connectivity
   - Check browser console for script errors

2. **Tamara Widget Issues**
   - Verify TAMARA_CDN and TAMARA_PUBLIC_KEY
   - Check Tamara service status
   - Ensure proper locale configuration

3. **Agency Deactivation**
   - Verify agency status in database
   - Check useAgencyStatus hook implementation
   - Ensure proper error messaging

4. **Payment Status Polling**
   - Check GraphQL endpoint availability
   - Verify polling interval configuration
   - Monitor for timeout issues

## Contributing

When contributing to the payment system:

1. Follow existing code patterns
2. Add comprehensive tests for new features
3. Update documentation for any API changes
4. Test with all supported payment methods
5. Verify agency-specific functionality
6. Ensure proper error handling and user feedback
