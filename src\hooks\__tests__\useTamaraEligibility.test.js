import { renderHook } from '@testing-library/react';
import useTamaraEligibility from '../useTamaraEligibility';

// Mock useFirebase hook
jest.mock('../useFirebase', () => ({
  __esModule: true,
  default: jest.fn(),
}));

const mockUseFirebase = require('../useFirebase').default;

describe('useTamaraEligibility Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('returns eligible when amount is within limits', () => {
    mockUseFirebase.mockReturnValue({
      remoteConfigValues: {
        remove_tamara_limit: { _value: 'false' },
        tamara_min_limit_value: { _value: '100' },
        tamara_max_limit_value: { _value: '5000' },
      },
    });

    const { result } = renderHook(() => useTamaraEligibility(1000));

    expect(result.current.isPriceEligible).toBe(true);
    expect(result.current.eligibilityDetails.isEligible).toBe(true);
    expect(result.current.eligibilityDetails.reason).toBe('Eligible for Tamara payment');
  });

  test('returns not eligible when amount is below minimum', () => {
    mockUseFirebase.mockReturnValue({
      remoteConfigValues: {
        remove_tamara_limit: { _value: 'false' },
        tamara_min_limit_value: { _value: '100' },
        tamara_max_limit_value: { _value: '5000' },
      },
    });

    const { result } = renderHook(() => useTamaraEligibility(50));

    expect(result.current.isPriceEligible).toBe(false);
    expect(result.current.eligibilityDetails.isEligible).toBe(false);
    expect(result.current.eligibilityDetails.reason).toContain('below minimum limit');
  });

  test('returns not eligible when amount is above maximum', () => {
    mockUseFirebase.mockReturnValue({
      remoteConfigValues: {
        remove_tamara_limit: { _value: 'false' },
        tamara_min_limit_value: { _value: '100' },
        tamara_max_limit_value: { _value: '5000' },
      },
    });

    const { result } = renderHook(() => useTamaraEligibility(6000));

    expect(result.current.isPriceEligible).toBe(false);
    expect(result.current.eligibilityDetails.isEligible).toBe(false);
    expect(result.current.eligibilityDetails.reason).toContain('above maximum limit');
  });

  test('returns eligible when limits are removed', () => {
    mockUseFirebase.mockReturnValue({
      remoteConfigValues: {
        remove_tamara_limit: { _value: 'true' },
        tamara_min_limit_value: { _value: '100' },
        tamara_max_limit_value: { _value: '5000' },
      },
    });

    const { result } = renderHook(() => useTamaraEligibility(10000));

    expect(result.current.isPriceEligible).toBe(true);
    expect(result.current.eligibilityDetails.limitsRemoved).toBe(true);
  });

  test('handles missing remote config values', () => {
    mockUseFirebase.mockReturnValue({
      remoteConfigValues: null,
    });

    const { result } = renderHook(() => useTamaraEligibility(1000));

    // Should use default values
    expect(result.current.tamaraConfig.tamaraMinLimit).toBe(0);
    expect(result.current.tamaraConfig.tamaraMaxLimit).toBe(999999);
    expect(result.current.isPriceEligible).toBe(true);
  });

  test('handles invalid amount values', () => {
    mockUseFirebase.mockReturnValue({
      remoteConfigValues: {
        remove_tamara_limit: { _value: 'false' },
        tamara_min_limit_value: { _value: '100' },
        tamara_max_limit_value: { _value: '5000' },
      },
    });

    const { result } = renderHook(() => useTamaraEligibility('invalid'));

    expect(result.current.eligibilityDetails.amount).toBe(0);
    expect(result.current.isPriceEligible).toBe(false);
  });

  test('handles zero amount', () => {
    mockUseFirebase.mockReturnValue({
      remoteConfigValues: {
        remove_tamara_limit: { _value: 'false' },
        tamara_min_limit_value: { _value: '100' },
        tamara_max_limit_value: { _value: '5000' },
      },
    });

    const { result } = renderHook(() => useTamaraEligibility(0));

    expect(result.current.isPriceEligible).toBe(false);
    expect(result.current.eligibilityDetails.reason).toContain('below minimum limit');
  });

  test('handles edge case amounts (exactly at limits)', () => {
    mockUseFirebase.mockReturnValue({
      remoteConfigValues: {
        remove_tamara_limit: { _value: 'false' },
        tamara_min_limit_value: { _value: '100' },
        tamara_max_limit_value: { _value: '5000' },
      },
    });

    // Test minimum limit
    const { result: minResult } = renderHook(() => useTamaraEligibility(100));
    expect(minResult.current.isPriceEligible).toBe(true);

    // Test maximum limit
    const { result: maxResult } = renderHook(() => useTamaraEligibility(5000));
    expect(maxResult.current.isPriceEligible).toBe(true);
  });

  test('provides correct eligibility details', () => {
    mockUseFirebase.mockReturnValue({
      remoteConfigValues: {
        remove_tamara_limit: { _value: 'false' },
        tamara_min_limit_value: { _value: '100' },
        tamara_max_limit_value: { _value: '5000' },
      },
    });

    const { result } = renderHook(() => useTamaraEligibility(1000));

    expect(result.current.eligibilityDetails).toEqual({
      amount: 1000,
      minLimit: 100,
      maxLimit: 5000,
      limitsRemoved: false,
      isEligible: true,
      reason: 'Eligible for Tamara payment',
    });
  });

  test('updates when amount changes', () => {
    mockUseFirebase.mockReturnValue({
      remoteConfigValues: {
        remove_tamara_limit: { _value: 'false' },
        tamara_min_limit_value: { _value: '100' },
        tamara_max_limit_value: { _value: '5000' },
      },
    });

    const { result, rerender } = renderHook(
      ({ amount }) => useTamaraEligibility(amount),
      { initialProps: { amount: 1000 } }
    );

    expect(result.current.isPriceEligible).toBe(true);

    // Change amount to below minimum
    rerender({ amount: 50 });
    expect(result.current.isPriceEligible).toBe(false);

    // Change amount to above maximum
    rerender({ amount: 6000 });
    expect(result.current.isPriceEligible).toBe(false);
  });

  test('handles string amounts correctly', () => {
    mockUseFirebase.mockReturnValue({
      remoteConfigValues: {
        remove_tamara_limit: { _value: 'false' },
        tamara_min_limit_value: { _value: '100' },
        tamara_max_limit_value: { _value: '5000' },
      },
    });

    const { result } = renderHook(() => useTamaraEligibility('1000'));

    expect(result.current.isPriceEligible).toBe(true);
    expect(result.current.eligibilityDetails.amount).toBe(1000);
  });
});
